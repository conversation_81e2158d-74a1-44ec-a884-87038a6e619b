import { NextRequest, NextResponse } from 'next/server'
import { verifyJWT } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

interface RouteParams {
  params: {
    id: string
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Await params to comply with Next.js 15 requirements
    const { id } = await params

    // Verify admin authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const payload = verifyJWT(token)
    if (!payload?.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const adminUser = await prisma.user.findUnique({
      where: { id: payload.userId }
    })

    if (!adminUser || adminUser.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Fetch the user with detailed information
    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        profile: {
          include: {
            town: true,
            profilePictures: {
              orderBy: { isPrimary: 'desc' }
            }
          }
        },
        subscription: {
          where: { status: 'ACTIVE' },
          orderBy: { createdAt: 'desc' },
          take: 1
        },
        _count: {
          select: {
            sentReports: true,
            receivedReports: true,
            matches: true,
            sentMessages: true
          }
        }
      }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Transform the data to match the expected format
    const userData = {
      id: user.id,
      email: user.email,
      name: user.name,
      isActive: user.isActive,
      isVerified: user.isVerified,
      subscriptionStatus: user.subscription?.[0]?.status || 'INACTIVE',
      createdAt: user.createdAt.toISOString(),
      lastActive: user.lastActive?.toISOString(),
      profile: user.profile ? {
        fullName: user.profile.fullName,
        gender: user.profile.gender,
        dateOfBirth: user.profile.dateOfBirth?.toISOString(),
        town: user.profile.town,
        tribe: user.profile.tribe,
        religion: user.profile.religion,
        occupation: user.profile.occupation,
        education: user.profile.education,
        relationshipGoal: user.profile.relationshipGoal,
        bio: user.profile.bio,
        profilePictures: user.profile.profilePictures.map(pic => ({
          url: pic.url,
          isPrimary: pic.isPrimary
        }))
      } : null,
      subscription: user.subscription?.[0] ? {
        status: user.subscription[0].status,
        expiresAt: user.subscription[0].expiresAt?.toISOString(),
        amount: user.subscription[0].amount
      } : null,
      _count: user._count
    }

    return NextResponse.json({ user: userData })
  } catch (error) {
    console.error('Get user profile error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
